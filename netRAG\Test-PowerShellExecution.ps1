# Test script for PowerShell execution via HTTP API

$baseUrl = "http://localhost:5000/api/PowerShellExecution"

Write-Host "Testing PowerShell Execution API..." -ForegroundColor Green

# Test 1: Health check
Write-Host "`n1. Testing health check..." -ForegroundColor Yellow
try {
    $healthResponse = Invoke-RestMethod -Uri "$baseUrl/health" -Method GET
    Write-Host "Health check result:" -ForegroundColor Cyan
    $healthResponse | ConvertTo-Json -Depth 3
} catch {
    Write-Host "Health check failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: Environment info
Write-Host "`n2. Testing environment info..." -ForegroundColor Yellow
try {
    $envResponse = Invoke-RestMethod -Uri "$baseUrl/environment" -Method GET
    Write-Host "Environment info:" -ForegroundColor Cyan
    $envResponse | ConvertTo-Json -Depth 3
} catch {
    Write-Host "Environment info failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: Simple command via GET
Write-Host "`n3. Testing simple command via GET..." -ForegroundColor Yellow
try {
    $getResponse = Invoke-RestMethod -Uri "$baseUrl/execute?command=Get-Date&timeout=10" -Method GET
    Write-Host "GET command result:" -ForegroundColor Cyan
    $getResponse | ConvertTo-Json -Depth 3
} catch {
    Write-Host "GET command failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4a: Debug the problematic command first
Write-Host "`n4a. Debugging the problematic command..." -ForegroundColor Yellow
try {
    $debugBody = @{
        command = "Get-Process | Where-Object { `$_.ProcessName -eq 'explorer' } | Select-Object ProcessName, Id, CPU"
        timeoutSeconds = 30
    } | ConvertTo-Json

    $debugResponse = Invoke-RestMethod -Uri "$baseUrl/debug" -Method POST -Body $debugBody -ContentType "application/json"
    Write-Host "Debug result:" -ForegroundColor Cyan
    $debugResponse | ConvertTo-Json -Depth 3
} catch {
    Write-Host "Debug failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4b: Simple PowerShell command via POST
Write-Host "`n4b. Testing simple PowerShell command via POST..." -ForegroundColor Yellow
try {
    $simpleBody = @{
        command = "Get-Process | Where-Object { `$_.ProcessName -eq 'explorer' } | Select-Object ProcessName, Id, CPU -First 1"
        timeoutSeconds = 30
    } | ConvertTo-Json

    $simpleResponse = Invoke-RestMethod -Uri "$baseUrl/execute" -Method POST -Body $simpleBody -ContentType "application/json"
    Write-Host "Simple POST command result:" -ForegroundColor Cyan
    $simpleResponse | ConvertTo-Json -Depth 3
} catch {
    Write-Host "Simple POST command failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4c: PowerShell command with Where-Object via POST
Write-Host "`n4c. Testing PowerShell command with Where-Object via POST..." -ForegroundColor Yellow
try {
    $postBody = @{
        command = "Get-Process | Where-Object { `$_.ProcessName -eq 'explorer' } | Select-Object ProcessName, Id, CPU"
        timeoutSeconds = 30
    } | ConvertTo-Json

    $postResponse = Invoke-RestMethod -Uri "$baseUrl/execute" -Method POST -Body $postBody -ContentType "application/json"
    Write-Host "POST command result:" -ForegroundColor Cyan
    $postResponse | ConvertTo-Json -Depth 3
} catch {
    Write-Host "POST command failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4d: Alternative syntax without Where-Object
Write-Host "`n4d. Testing alternative syntax without Where-Object..." -ForegroundColor Yellow
try {
    $altBody = @{
        command = "Get-Process -Name explorer | Select-Object ProcessName, Id, CPU"
        timeoutSeconds = 30
    } | ConvertTo-Json

    $altResponse = Invoke-RestMethod -Uri "$baseUrl/execute" -Method POST -Body $altBody -ContentType "application/json"
    Write-Host "Alternative syntax result:" -ForegroundColor Cyan
    $altResponse | ConvertTo-Json -Depth 3
} catch {
    Write-Host "Alternative syntax failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4e: Basic PowerShell functionality test
Write-Host "`n4e. Testing basic PowerShell functionality..." -ForegroundColor Yellow
try {
    $basicBody = @{
        command = "Get-Service | Where-Object { `$_.Status -eq 'Running' } | Select-Object Name, Status -First 3"
        timeoutSeconds = 30
    } | ConvertTo-Json

    $basicResponse = Invoke-RestMethod -Uri "$baseUrl/execute" -Method POST -Body $basicBody -ContentType "application/json"
    Write-Host "Basic functionality result:" -ForegroundColor Cyan
    $basicResponse | ConvertTo-Json -Depth 3
} catch {
    Write-Host "Basic functionality failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 5a: Test Active Directory module availability
Write-Host "`n5a. Testing Active Directory module availability..." -ForegroundColor Yellow
try {
    $adModuleBody = @{
        command = "Get-Module -ListAvailable -Name ActiveDirectory | Select-Object Name, Version, ModuleBase"
        timeoutSeconds = 30
    } | ConvertTo-Json

    $adModuleResponse = Invoke-RestMethod -Uri "$baseUrl/execute" -Method POST -Body $adModuleBody -ContentType "application/json"
    Write-Host "AD module availability result:" -ForegroundColor Cyan
    $adModuleResponse | ConvertTo-Json -Depth 3
} catch {
    Write-Host "AD module check failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 5b: Test manual Active Directory module import
Write-Host "`n5b. Testing manual Active Directory module import..." -ForegroundColor Yellow
try {
    $adImportBody = @{
        command = "Import-Module ActiveDirectory -ErrorAction SilentlyContinue; Get-Module ActiveDirectory | Select-Object Name, Version"
        timeoutSeconds = 30
    } | ConvertTo-Json

    $adImportResponse = Invoke-RestMethod -Uri "$baseUrl/execute" -Method POST -Body $adImportBody -ContentType "application/json"
    Write-Host "AD module import result:" -ForegroundColor Cyan
    $adImportResponse | ConvertTo-Json -Depth 3
} catch {
    Write-Host "AD module import failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 5c: Testing Active Directory connectivity (original test)
Write-Host "`n5c. Testing Active Directory connectivity..." -ForegroundColor Yellow
try {
    $adResponse = Invoke-RestMethod -Uri "$baseUrl/test-ad" -Method GET
    Write-Host "AD test result:" -ForegroundColor Cyan
    $adResponse | ConvertTo-Json -Depth 3
} catch {
    Write-Host "AD test failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 6: Complex PowerShell command with server scope
Write-Host "`n6. Testing complex command with server scope..." -ForegroundColor Yellow
try {
    $complexBody = @{
        command = "Get-ADUser -Identity Administrator -Properties * | Select-Object Name, SamAccountName, Enabled, LastLogonDate"
        timeoutSeconds = 60
    } | ConvertTo-Json

    $complexResponse = Invoke-RestMethod -Uri "$baseUrl/execute" -Method POST -Body $complexBody -ContentType "application/json"
    Write-Host "Complex command result:" -ForegroundColor Cyan
    $complexResponse | ConvertTo-Json -Depth 3
} catch {
    Write-Host "Complex command failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 7: Test command that would benefit from server scope
Write-Host "`n7. Testing command that should get server scope automatically..." -ForegroundColor Yellow
try {
    $serverScopeBody = @{
        command = "Get-ADDomain | Select-Object Name, DNSRoot, DomainMode"
        timeoutSeconds = 30
    } | ConvertTo-Json

    $serverScopeResponse = Invoke-RestMethod -Uri "$baseUrl/execute" -Method POST -Body $serverScopeBody -ContentType "application/json"
    Write-Host "Server scope command result:" -ForegroundColor Cyan
    $serverScopeResponse | ConvertTo-Json -Depth 3
} catch {
    Write-Host "Server scope command failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 8: Test error handling with invalid command
Write-Host "`n8. Testing error handling with invalid command..." -ForegroundColor Yellow
try {
    $errorBody = @{
        command = "Get-NonExistentCommand -BadParameter"
        timeoutSeconds = 10
    } | ConvertTo-Json

    $errorResponse = Invoke-RestMethod -Uri "$baseUrl/execute" -Method POST -Body $errorBody -ContentType "application/json"
    Write-Host "Error handling result:" -ForegroundColor Cyan
    $errorResponse | ConvertTo-Json -Depth 3
} catch {
    Write-Host "Error test failed (this might be expected): $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nTesting completed!" -ForegroundColor Green
Write-Host "Summary:" -ForegroundColor Yellow
Write-Host "- Health check: Tests basic service availability" -ForegroundColor White
Write-Host "- Environment info: Shows PowerShell runtime details" -ForegroundColor White
Write-Host "- GET execution: Simple command via query parameter" -ForegroundColor White
Write-Host "- POST execution: Complex command via JSON body" -ForegroundColor White
Write-Host "- AD connectivity: Tests Active Directory module availability" -ForegroundColor White
Write-Host "- Server scope: Tests automatic server parameter injection" -ForegroundColor White
Write-Host "- Error handling: Tests service response to invalid commands" -ForegroundColor White

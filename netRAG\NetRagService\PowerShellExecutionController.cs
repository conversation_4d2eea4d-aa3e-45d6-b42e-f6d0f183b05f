using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace NetRagService;

[ApiController]
[Route("api/[controller]")]
public class PowerShellExecutionController : ControllerBase
{
    private readonly PowerShellExecutionService _executionService;
    private readonly ILogger<PowerShellExecutionController> _logger;

    public PowerShellExecutionController(
        PowerShellExecutionService executionService,
        ILogger<PowerShellExecutionController> logger)
    {
        _executionService = executionService;
        _logger = logger;
    }

    /// <summary>
    /// Execute a PowerShell command
    /// </summary>
    /// <param name="request">PowerShell execution request</param>
    /// <returns>Execution result</returns>
    [HttpPost("execute")]
    public async Task<ActionResult<PowerShellExecutionResponse>> ExecuteCommand(
        [FromBody] PowerShellExecutionRequest request)
    {
        if (request == null)
        {
            return BadRequest("Request body is required");
        }

        if (string.IsNullOrWhiteSpace(request.Command))
        {
            return BadRequest("Command is required");
        }

        // Validate timeout
        if (request.TimeoutSeconds <= 0 || request.TimeoutSeconds > 300)
        {
            return BadRequest("Timeout must be between 1 and 300 seconds");
        }

        try
        {
            _logger.LogInformation("Received PowerShell execution request for command: {Command}",
                request.Command.Length > 100 ? request.Command.Substring(0, 100) + "..." : request.Command);

            // Log the full command for debugging
            _logger.LogDebug("Full command received: {FullCommand}", request.Command);
            _logger.LogDebug("Command bytes: {CommandBytes}",
                string.Join(" ", System.Text.Encoding.UTF8.GetBytes(request.Command).Select(b => b.ToString("X2"))));

            var response = await _executionService.ExecuteCommandAsync(request);
            
            if (response.Success)
            {
                _logger.LogInformation("PowerShell command executed successfully in {ExecutionTime}ms", 
                    response.ExecutionTimeMs);
            }
            else
            {
                _logger.LogWarning("PowerShell command failed with error: {Error}", response.Error);
            }

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to execute PowerShell command");
            return StatusCode(500, new PowerShellExecutionResponse
            {
                Success = false,
                Error = "Internal server error occurred during PowerShell execution",
                ExitCode = -1,
                ExecutionTimeMs = 0,
                Timestamp = DateTime.UtcNow
            });
        }
    }

    /// <summary>
    /// Execute a simple PowerShell command via GET (for testing)
    /// </summary>
    /// <param name="command">PowerShell command to execute</param>
    /// <param name="timeout">Timeout in seconds (default: 30)</param>
    /// <returns>Execution result</returns>
    [HttpGet("execute")]
    public async Task<ActionResult<PowerShellExecutionResponse>> ExecuteCommandGet(
        [FromQuery] string command,
        [FromQuery] int timeout = 30)
    {
        if (string.IsNullOrWhiteSpace(command))
        {
            return BadRequest("Command parameter is required");
        }

        var request = new PowerShellExecutionRequest
        {
            Command = command,
            TimeoutSeconds = Math.Min(Math.Max(timeout, 1), 120) // Limit GET requests to 2 minutes
        };

        return await ExecuteCommand(request);
    }

    /// <summary>
    /// Get PowerShell environment information
    /// </summary>
    /// <returns>Environment details</returns>
    [HttpGet("environment")]
    public async Task<ActionResult<Dictionary<string, object>>> GetEnvironment()
    {
        try
        {
            var environmentInfo = await _executionService.GetEnvironmentInfoAsync();
            return Ok(environmentInfo);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get PowerShell environment information");
            return StatusCode(500, new Dictionary<string, object>
            {
                ["error"] = "Failed to get environment information",
                ["details"] = ex.Message
            });
        }
    }

    /// <summary>
    /// Health check endpoint for PowerShell execution service
    /// </summary>
    /// <returns>Health status</returns>
    [HttpGet("health")]
    public async Task<ActionResult<object>> HealthCheck()
    {
        try
        {
            // Test with a simple command
            var testRequest = new PowerShellExecutionRequest
            {
                Command = "Get-Date",
                TimeoutSeconds = 10
            };

            var testResponse = await _executionService.ExecuteCommandAsync(testRequest);

            if (testResponse.Success)
            {
                return Ok(new
                {
                    status = "healthy",
                    service = "PowerShell Execution",
                    test_execution_time_ms = testResponse.ExecutionTimeMs,
                    timestamp = DateTime.UtcNow.ToString("O")
                });
            }
            else
            {
                return StatusCode(500, new
                {
                    status = "unhealthy",
                    service = "PowerShell Execution",
                    error = testResponse.Error,
                    timestamp = DateTime.UtcNow.ToString("O")
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "PowerShell execution health check failed");
            return StatusCode(500, new
            {
                status = "unhealthy",
                service = "PowerShell Execution",
                error = ex.Message,
                timestamp = DateTime.UtcNow.ToString("O")
            });
        }
    }

    /// <summary>
    /// Test Active Directory connectivity
    /// </summary>
    /// <returns>AD connectivity test result</returns>
    [HttpGet("test-ad")]
    public async Task<ActionResult<PowerShellExecutionResponse>> TestActiveDirectory()
    {
        try
        {
            var testRequest = new PowerShellExecutionRequest
            {
                Command = "Get-ADDomain | Select-Object Name, DNSRoot, DomainMode",
                TimeoutSeconds = 30
            };

            var response = await _executionService.ExecuteCommandAsync(testRequest);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to test Active Directory connectivity");
            return StatusCode(500, new PowerShellExecutionResponse
            {
                Success = false,
                Error = "Failed to test Active Directory connectivity: " + ex.Message,
                ExitCode = -1,
                ExecutionTimeMs = 0,
                Timestamp = DateTime.UtcNow
            });
        }
    }

    /// <summary>
    /// Debug endpoint to inspect command as received
    /// </summary>
    /// <param name="request">PowerShell execution request</param>
    /// <returns>Debug information about the request</returns>
    [HttpPost("debug")]
    public ActionResult<object> DebugCommand([FromBody] PowerShellExecutionRequest request)
    {
        if (request == null)
        {
            return BadRequest("Request body is required");
        }

        _logger.LogInformation("Debug request received");

        return Ok(new
        {
            received_command = request.Command,
            command_length = request.Command?.Length ?? 0,
            command_bytes = System.Text.Encoding.UTF8.GetBytes(request.Command ?? ""),
            timeout_seconds = request.TimeoutSeconds,
            parameters = request.Parameters,
            timestamp = DateTime.UtcNow.ToString("O")
        });
    }
}

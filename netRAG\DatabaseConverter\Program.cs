using Microsoft.Extensions.AI;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using NetRagService;
using System.IO.Compression;
using System.Text.Json;

namespace DatabaseConverter;

/// <summary>
/// Standalone utility to convert Microsoft Learn JSON data to compressed binary database files.
/// This is a one-time operation that should be run separately from the main service.
/// </summary>
class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("=== NetRAG Database Converter ===");
        Console.WriteLine("Converts Microsoft Learn JSON data to compressed binary database files.");
        Console.WriteLine();

        if (args.Length < 2)
        {
            Console.WriteLine("Usage: DatabaseConverter.exe <input-json-file> <output-database-file>");
            Console.WriteLine("Example: DatabaseConverter.exe microsoft_learn_data.json database.bin");
            Environment.Exit(1);
        }

        string inputJsonFile = args[0];
        string outputDatabaseFile = args[1];

        if (!File.Exists(inputJsonFile))
        {
            Console.WriteLine($"Error: Input file '{inputJsonFile}' not found.");
            Environment.Exit(1);
        }

        try
        {
            await ConvertJsonToDatabase(inputJsonFile, outputDatabaseFile);
            Console.WriteLine($"✅ Conversion completed successfully!");
            Console.WriteLine($"📁 Output file: {outputDatabaseFile}");
            Console.WriteLine($"📊 File size: {new FileInfo(outputDatabaseFile).Length:N0} bytes");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Error during conversion: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
            Environment.Exit(1);
        }
    }

    static async Task ConvertJsonToDatabase(string inputJsonFile, string outputDatabaseFile)
    {
        Console.WriteLine($"📖 Reading input file: {inputJsonFile}");
        
        // Set up dependency injection and services
        var services = new ServiceCollection();
        services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Information));
        
        // Configure Semantic Kernel with BERT ONNX embedding
        var kernelBuilder = Kernel.CreateBuilder();
        
        // Use default model paths - adjust these if your models are in different locations
        string modelPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "model.onnx");
        string vocabPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "vocab.txt");
        
        if (!File.Exists(modelPath))
        {
            throw new FileNotFoundException($"BERT ONNX model not found at: {modelPath}");
        }
        
        if (!File.Exists(vocabPath))
        {
            throw new FileNotFoundException($"BERT vocabulary file not found at: {vocabPath}");
        }
        
        Console.WriteLine($"🧠 Loading BERT embedding model: {modelPath}");
        kernelBuilder.AddBertOnnxEmbeddingGenerator(modelPath, vocabPath);
        
        var kernel = kernelBuilder.Build();
        var embeddingService = kernel.GetRequiredService<IEmbeddingGenerator<string, Microsoft.Extensions.AI.Embedding<float>>>();
        
        services.AddSingleton(embeddingService);
        services.AddSingleton<VectorStoreService>();


        var serviceProvider = services.BuildServiceProvider();
        var logger = serviceProvider.GetRequiredService<ILogger<Program>>();

        // Get the vector store from DI and initialize it
        var vectorStore = serviceProvider.GetRequiredService<VectorStoreService>();
        await vectorStore.InitializeAsync(768); // Standard BERT embedding size
        
        Console.WriteLine($"⚡ Processing PowerShell commands from JSON...");
        
        // Read and parse JSON file - detect format
        string jsonContent = await File.ReadAllTextAsync(inputJsonFile);

        List<PowerShellCommandData> commands;

        // Try to detect if this is RAG-optimized format or legacy format
        if (jsonContent.TrimStart().StartsWith("{") && jsonContent.Contains("\"commands\""))
        {
            // RAG-optimized format
            Console.WriteLine("📊 Detected RAG-optimized JSON format");
            var ragData = JsonSerializer.Deserialize<RagOptimizedData>(jsonContent, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            if (ragData?.Commands == null || ragData.Commands.Count == 0)
            {
                throw new InvalidOperationException("No PowerShell commands found in the RAG-optimized input file.");
            }

            // Convert RAG-optimized commands to legacy format for processing
            commands = ragData.Commands.Select(ragCmd => new PowerShellCommandData
            {
                CommandName = ragCmd.CommandName,
                Synopsis = ragCmd.PrimaryPurpose,
                Description = ragCmd.Description,
                Module = ragCmd.Module,
                Parameters = ragCmd.Parameters,
                Examples = ragCmd.Examples
            }).ToList();

            Console.WriteLine($"📋 Found {commands.Count} RAG-optimized PowerShell commands to process");
        }
        else
        {
            // Legacy format
            Console.WriteLine("📊 Detected legacy JSON format");
            commands = JsonSerializer.Deserialize<List<PowerShellCommandData>>(jsonContent);

            if (commands == null || commands.Count == 0)
            {
                throw new InvalidOperationException("No PowerShell commands found in the input file.");
            }

            Console.WriteLine($"📋 Found {commands.Count} PowerShell commands to process");
        }
        
        // Process each command and generate embeddings
        int processed = 0;
        foreach (var command in commands)
        {
            try
            {
                // Create searchable text combining command name, synopsis, description, and parameters
                var searchableText = $"{command.CommandName} {command.Synopsis} {command.Description}";
                if (command.Parameters?.Any() == true)
                {
                    searchableText += " " + string.Join(" ", command.Parameters.Select(p => $"{p.Name} {p.Description}"));
                }
                
                // Generate embedding
                var embeddingResults = await embeddingService.GenerateAsync(new[] { searchableText });
                var embedding = embeddingResults.First();
                
                // Create metadata
                var metadata = new Dictionary<string, object>
                {
                    ["commandName"] = command.CommandName ?? "",
                    ["synopsis"] = command.Synopsis ?? "",
                    ["description"] = command.Description ?? "",
                    ["module"] = command.Module ?? "",
                    ["parameterCount"] = command.Parameters?.Count ?? 0,
                    ["exampleCount"] = command.Examples?.Count ?? 0,
                    ["fullText"] = searchableText
                };
                
                // Add parameter information
                if (command.Parameters?.Any() == true)
                {
                    metadata["parameterNames"] = string.Join(", ", command.Parameters.Select(p => p.Name));
                    metadata["parameters"] = JsonSerializer.Serialize(command.Parameters);
                }
                
                // Add example information
                if (command.Examples?.Any() == true)
                {
                    metadata["examples"] = JsonSerializer.Serialize(command.Examples);
                }
                
                // Add to vector store
                await vectorStore.UpsertAsync(command.CommandName ?? $"command_{processed}", embedding.Vector, metadata);
                
                processed++;
                if (processed % 100 == 0)
                {
                    Console.WriteLine($"⏳ Processed {processed}/{commands.Count} commands...");
                }
            }
            catch (Exception ex)
            {
                logger.LogWarning("Failed to process command {CommandName}: {Error}", command.CommandName, ex.Message);
            }
        }
        
        Console.WriteLine($"✨ Processed {processed} commands successfully");
        Console.WriteLine($"💾 Saving to database file: {outputDatabaseFile}");

        // Save to compressed binary format using the VectorStoreService method
        await vectorStore.SaveToDatabaseFileAsync(outputDatabaseFile);

        Console.WriteLine($"🎉 Database conversion completed!");
    }
    
    // SaveVectorStoreToFile method removed - now using VectorStoreService.SaveToDatabaseFileAsync()
}

// Data structures are now in DataModels.cs to avoid duplication
